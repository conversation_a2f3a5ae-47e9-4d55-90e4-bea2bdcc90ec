#!/bin/bash

echo "=== VSCode 沙箱网络修复方案 ==="

# 方案1: 禁用网络沙箱启动 VSCode
echo "方案1: 禁用网络沙箱启动 VSCode"
echo "命令: code --disable-web-security --disable-features=VizDisplayCompositor --no-sandbox"

# 方案2: 修改 VSCode 配置
echo "方案2: 修改 VSCode 用户设置"
echo "在 VSCode 设置中添加："
echo '{
  "terminal.integrated.inheritEnv": false,
  "terminal.integrated.env.osx": {
    "PATH": "/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin"
  }
}'

# 方案3: 使用系统终端
echo "方案3: 使用系统终端 (推荐)"
echo "在外部终端中运行网络相关命令"

# 方案4: 重置网络权限
echo "方案4: 重置 VSCode 网络权限"
echo "1. 完全退出 VSCode"
echo "2. 删除网络缓存: rm -rf ~/Library/Caches/com.microsoft.VSCode"
echo "3. 重新启动 VSCode"

# 测试当前网络状态
echo "=== 当前网络测试 ==="
echo "测试外网连接..."
curl -s --connect-timeout 3 http://www.google.com > /dev/null
if [ $? -eq 0 ]; then
    echo "外网: ✅"
else
    echo "外网: ❌"
fi

echo "测试本地网络..."
ping -c 1 *********** > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "本地网络: ✅"
else
    echo "本地网络: ❌ (这是问题所在)"
fi

echo "=== 立即可用的解决方案 ==="
echo "在外部终端中执行:"
echo "ssh -o HostKeyAlgorithms=+ssh-rsa,ssh-dss hkria@************"
echo "或者"
echo "nc -zv ************ 3389"
