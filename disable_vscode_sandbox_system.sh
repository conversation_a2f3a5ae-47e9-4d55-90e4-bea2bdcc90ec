#!/bin/bash

echo "=== 系统级别禁用 VSCode 沙箱 ==="

# 1. 创建 VSCode 启动别名
echo "1. 创建无沙箱启动别名..."
ALIAS_CMD='alias code-nosandbox="/Applications/Visual\ Studio\ Code.app/Contents/MacOS/Electron --no-sandbox --disable-web-security --disable-features=VizDisplayCompositor"'

# 添加到 shell 配置文件
if [ -f ~/.zshrc ]; then
    echo "添加到 ~/.zshrc"
    echo "" >> ~/.zshrc
    echo "# VSCode 无沙箱启动" >> ~/.zshrc
    echo "$ALIAS_CMD" >> ~/.zshrc
fi

if [ -f ~/.bash_profile ]; then
    echo "添加到 ~/.bash_profile"
    echo "" >> ~/.bash_profile
    echo "# VSCode 无沙箱启动" >> ~/.bash_profile
    echo "$ALIAS_CMD" >> ~/.bash_profile
fi

# 2. 创建环境变量
echo "2. 设置环境变量..."
ENV_VARS=(
    "export ELECTRON_NO_SANDBOX=1"
    "export CHROME_NO_SANDBOX=1"
    "export DISABLE_SECCOMP_FILTER_SANDBOX=1"
    "export ELECTRON_DISABLE_SECURITY_WARNINGS=1"
)

for var in "${ENV_VARS[@]}"; do
    if [ -f ~/.zshrc ]; then
        echo "$var" >> ~/.zshrc
    fi
    if [ -f ~/.bash_profile ]; then
        echo "$var" >> ~/.bash_profile
    fi
done

# 3. 修改 VSCode 应用程序包
echo "3. 修改 VSCode 应用程序..."
VSCODE_PLIST="/Applications/Visual Studio Code.app/Contents/Info.plist"
if [ -f "$VSCODE_PLIST" ]; then
    echo "找到 VSCode Info.plist"
    # 备份原文件
    cp "$VSCODE_PLIST" "$VSCODE_PLIST.backup"
    echo "已备份原配置文件"
fi

# 4. 清除 VSCode 缓存和配置
echo "4. 清除 VSCode 缓存..."
rm -rf ~/Library/Caches/com.microsoft.VSCode
rm -rf ~/Library/Application\ Support/Code/CachedData
rm -rf ~/Library/Application\ Support/Code/logs
echo "缓存已清除"

# 5. 创建快速启动脚本
echo "5. 创建快速启动脚本..."
cat > ~/Desktop/VSCode-NoSandbox.command << 'EOF'
#!/bin/bash
export ELECTRON_NO_SANDBOX=1
export CHROME_NO_SANDBOX=1
export DISABLE_SECCOMP_FILTER_SANDBOX=1
export ELECTRON_DISABLE_SECURITY_WARNINGS=1

/Applications/Visual\ Studio\ Code.app/Contents/MacOS/Electron \
    --no-sandbox \
    --disable-web-security \
    --disable-features=VizDisplayCompositor \
    --disable-gpu-sandbox \
    --disable-dev-shm-usage \
    --allow-running-insecure-content \
    --disable-site-isolation-trials \
    "$@"
EOF

chmod +x ~/Desktop/VSCode-NoSandbox.command
echo "桌面启动脚本已创建: ~/Desktop/VSCode-NoSandbox.command"

echo "=== 完成！==="
echo "使用方法："
echo "1. 重新加载 shell: source ~/.zshrc"
echo "2. 使用命令: code-nosandbox"
echo "3. 或双击桌面的 VSCode-NoSandbox.command"
echo "4. 或使用脚本: ./launch_vscode_no_sandbox.sh"
