#!/bin/bash

echo "=== IDE 网络修复脚本 ==="

# 1. 检查当前网络状态
echo "1. 检查当前网络接口状态..."
ifconfig en0 | grep "inet "

# 2. 检查路由表
echo "2. 检查路由表..."
netstat -rn | grep "192.168.2"

# 3. 尝试刷新 ARP 缓存
echo "3. 刷新 ARP 缓存..."
arp -a | grep "192.168.2.50"

# 4. 测试基本连接
echo "4. 测试基本连接..."
ping -c 1 192.168.2.1  # 测试网关

# 5. 尝试重新绑定网络接口
echo "5. 建议的修复命令（需要管理员权限）："
echo "sudo ifconfig en0 down"
echo "sudo ifconfig en0 up"
echo "sudo route flush"

# 6. 替代方案
echo "6. 替代方案："
echo "- 重启 IDE"
echo "- 使用外部终端进行网络操作"
echo "- 检查 IDE 的网络权限设置"

echo "=== 修复脚本完成 ==="
