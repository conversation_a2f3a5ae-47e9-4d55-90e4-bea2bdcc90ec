#!/bin/bash

# Sony Song Import 运行脚本
# 解决IDE网络问题的临时方案

echo "=== Sony Song Import 运行脚本 ==="
echo "当前时间: $(date)"
echo "工作目录: $(pwd)"
echo "网络测试: ping 192.168.2.50"

# 测试网络连通性
ping -c 2 192.168.2.50
if [ $? -eq 0 ]; then
    echo "✅ 网络连通正常"
else
    echo "❌ 网络连通失败"
    exit 1
fi

# 测试SSH端口
echo "测试SSH端口22..."
nc -zv 192.168.2.50 22
if [ $? -eq 0 ]; then
    echo "✅ SSH端口22开放"
else
    echo "❌ SSH端口22不可达"
fi

echo ""
echo "=== 开始运行Sony Song Import ==="
echo "参数: $1"

# 编译程序
echo "编译程序..."
xbuild "Sony Song Import.csproj"
if [ $? -ne 0 ]; then
    echo "❌ 编译失败"
    exit 1
fi

# 运行程序
echo "运行程序..."
mono "bin/Debug/Sony Song Import.exe" $1

echo "=== 程序运行完成 ==="
