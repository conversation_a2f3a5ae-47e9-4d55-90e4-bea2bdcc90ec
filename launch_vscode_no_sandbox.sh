#!/bin/bash

echo "=== 启动无沙箱 VSCode ==="

# 完全禁用所有沙箱和安全限制
VSCODE_ARGS=(
    --no-sandbox
    --disable-web-security
    --disable-features=VizDisplayCompositor
    --disable-dev-shm-usage
    --disable-gpu-sandbox
    --disable-software-rasterizer
    --disable-background-timer-throttling
    --disable-backgrounding-occluded-windows
    --disable-renderer-backgrounding
    --disable-field-trial-config
    --disable-ipc-flooding-protection
    --allow-running-insecure-content
    --disable-site-isolation-trials
    --disable-extensions-except
    --disable-default-apps
    --disable-sync
    --disable-translate
    --disable-background-networking
    --disable-background-mode
    --disable-client-side-phishing-detection
    --disable-component-extensions-with-background-pages
    --disable-default-apps
    --disable-hang-monitor
    --disable-prompt-on-repost
    --disable-domain-reliability
    --disable-features=TranslateUI
    --disable-features=BlinkGenPropertyTrees
    --enable-logging=stderr
    --log-level=0
)

echo "禁用的安全特性："
echo "- 网络沙箱"
echo "- GPU 沙箱"
echo "- 渲染器沙箱"
echo "- IPC 保护"
echo "- 站点隔离"
echo "- 所有后台限制"

echo "启动 VSCode..."
/Applications/Visual\ Studio\ Code.app/Contents/MacOS/Electron "${VSCODE_ARGS[@]}" "$@"
