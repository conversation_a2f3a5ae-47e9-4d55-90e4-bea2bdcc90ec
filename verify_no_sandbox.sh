#!/bin/bash

echo "=== 验证 VSCode 沙箱状态 ==="

# 1. 检查当前 VSCode 进程
echo "1. 检查当前 VSCode 进程的启动参数..."
VSCODE_PROCESSES=$(ps aux | grep -E "Visual Studio Code|Electron" | grep -v grep)

if [ -z "$VSCODE_PROCESSES" ]; then
    echo "❌ 没有找到运行中的 VSCode 进程"
    echo "请先启动 VSCode 再运行此脚本"
    exit 1
fi

echo "找到 VSCode 进程："
echo "$VSCODE_PROCESSES" | head -3

# 2. 检查是否还有沙箱参数
echo ""
echo "2. 检查沙箱参数..."
SANDBOX_COUNT=$(echo "$VSCODE_PROCESSES" | grep -c "enable-sandbox")
NETWORK_SANDBOX_COUNT=$(echo "$VSCODE_PROCESSES" | grep -c "service-sandbox-type=network")

if [ $SANDBOX_COUNT -gt 0 ]; then
    echo "⚠️  仍然发现 $SANDBOX_COUNT 个进程使用 --enable-sandbox"
else
    echo "✅ 没有发现 --enable-sandbox 参数"
fi

if [ $NETWORK_SANDBOX_COUNT -gt 0 ]; then
    echo "⚠️  仍然发现 $NETWORK_SANDBOX_COUNT 个进程使用网络沙箱"
else
    echo "✅ 没有发现网络沙箱参数"
fi

# 3. 测试网络连接
echo ""
echo "3. 测试网络连接..."

# 测试 ping
ping -c 1 192.168.2.50 > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ Ping 192.168.2.50: 成功"
else
    echo "❌ Ping 192.168.2.50: 失败"
fi

# 测试端口连接
nc -z 192.168.2.50 3389 > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ 端口 3389: 开放"
else
    echo "❌ 端口 3389: 关闭"
fi

nc -z 192.168.2.50 22 > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ 端口 22: 开放"
else
    echo "❌ 端口 22: 关闭"
fi

# 4. 检查环境变量
echo ""
echo "4. 检查环境变量..."
echo "ELECTRON_NO_SANDBOX: ${ELECTRON_NO_SANDBOX:-未设置}"
echo "CHROME_NO_SANDBOX: ${CHROME_NO_SANDBOX:-未设置}"
echo "DISABLE_SECCOMP_FILTER_SANDBOX: ${DISABLE_SECCOMP_FILTER_SANDBOX:-未设置}"

# 5. 总结
echo ""
echo "=== 总结 ==="
if [ $SANDBOX_COUNT -eq 0 ] && [ $NETWORK_SANDBOX_COUNT -eq 0 ]; then
    echo "🎉 VSCode 沙箱已完全禁用！"
    echo "现在通过任何方式启动 VSCode 都不会有沙箱限制"
else
    echo "⚠️  VSCode 可能仍有部分沙箱限制"
    echo "建议重启 VSCode 或检查修改是否生效"
fi
