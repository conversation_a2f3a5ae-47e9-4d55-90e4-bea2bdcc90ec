#!/bin/bash

echo "=== VSCode 网络修复脚本 ==="

# 1. 检查当前 VSCode 进程的网络权限
echo "1. 检查 VSCode 网络进程..."
lsof -i | grep -E "(Code|vscode)" | head -3

# 2. 尝试重置网络配置
echo "2. 重置网络配置..."
networksetup -detectnewhardware

# 3. 刷新 DNS 缓存
echo "3. 刷新 DNS 缓存..."
dscacheutil -flushcache

# 4. 检查网络接口状态
echo "4. 检查网络接口..."
ifconfig en0 | grep "status"

# 5. 测试网络连接
echo "5. 测试网络连接..."
ping -c 1 8.8.8.8 > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "外网连接: ✅ 正常"
else
    echo "外网连接: ❌ 异常"
fi

ping -c 1 192.168.2.1 > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "网关连接: ✅ 正常"
else
    echo "网关连接: ❌ 异常"
fi

ping -c 1 ************ > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "目标主机: ✅ 正常"
else
    echo "目标主机: ❌ 异常"
fi

echo "=== 修复建议 ==="
echo "如果问题仍然存在，请尝试："
echo "1. 完全退出 VSCode 并重新启动"
echo "2. 重启系统网络服务"
echo "3. 检查 macOS 系统偏好设置 > 安全性与隐私 > 防火墙"
echo "4. 检查 VSCode 的网络权限设置"

echo "=== 临时解决方案 ==="
echo "使用外部终端进行网络操作："
echo "ssh -o HostKeyAlgorithms=+ssh-rsa,ssh-dss hkria@************"
