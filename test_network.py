#!/usr/bin/env python3
import socket
import subprocess
import sys
import os

def test_ping():
    """测试 ping 连接"""
    try:
        result = subprocess.run(['ping', '-c', '3', '************'], 
                              capture_output=True, text=True, timeout=15)
        print("=== PING 测试 ===")
        print(f"返回码: {result.returncode}")
        print(f"输出: {result.stdout}")
        if result.stderr:
            print(f"错误: {result.stderr}")
        return result.returncode == 0
    except Exception as e:
        print(f"Ping 测试失败: {e}")
        return False

def test_socket_connection(host, port):
    """测试 socket 连接"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except Exception as e:
        print(f"Socket 连接失败: {e}")
        return False

def main():
    print("=== 网络连接测试 ===")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"Python 版本: {sys.version}")
    
    # 测试 ping
    ping_success = test_ping()
    
    # 测试端口连接
    print("\n=== 端口连接测试 ===")
    
    # 测试 SSH (22)
    ssh_open = test_socket_connection('************', 22)
    print(f"SSH 端口 22: {'开放' if ssh_open else '关闭/过滤'}")
    
    # 测试 RDP (3389)
    rdp_open = test_socket_connection('************', 3389)
    print(f"RDP 端口 3389: {'开放' if rdp_open else '关闭/过滤'}")
    
    # 总结
    print("\n=== 测试总结 ===")
    print(f"Ping: {'成功' if ping_success else '失败'}")
    print(f"SSH: {'可连接' if ssh_open else '不可连接'}")
    print(f"RDP: {'可连接' if rdp_open else '不可连接'}")

if __name__ == "__main__":
    main()
