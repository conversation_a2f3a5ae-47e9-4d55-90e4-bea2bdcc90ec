#!/bin/bash

echo "=== 彻底修改 VSCode 应用程序包 ==="

VSCODE_APP="/Applications/Visual Studio Code.app"
VSCODE_ELECTRON="$VSCODE_APP/Contents/MacOS/Electron"
VSCODE_INFO_PLIST="$VSCODE_APP/Contents/Info.plist"

# 1. 备份原始文件
echo "1. 备份原始文件..."
if [ -f "$VSCODE_ELECTRON" ]; then
    cp "$VSCODE_ELECTRON" "$VSCODE_ELECTRON.original"
    echo "已备份: $VSCODE_ELECTRON.original"
fi

# 2. 创建无沙箱启动脚本替换原始 Electron
echo "2. 创建无沙箱启动脚本..."
cat > "$VSCODE_ELECTRON.new" << 'EOF'
#!/bin/bash

# 设置无沙箱环境变量
export ELECTRON_NO_SANDBOX=1
export CHROME_NO_SANDBOX=1
export DISABLE_SECCOMP_FILTER_SANDBOX=1
export ELECTRON_DISABLE_SECURITY_WARNINGS=1

# 获取应用程序路径
APP_PATH="$(dirname "$0")/../.."
ELECTRON_BINARY="$APP_PATH/Contents/MacOS/Electron.original"

# 如果没有原始备份，使用当前文件
if [ ! -f "$ELECTRON_BINARY" ]; then
    ELECTRON_BINARY="$APP_PATH/Contents/MacOS/Electron.original"
    if [ ! -f "$ELECTRON_BINARY" ]; then
        # 寻找真正的 Electron 二进制文件
        ELECTRON_BINARY="$APP_PATH/Contents/Frameworks/Electron Framework.framework/Versions/A/Electron Framework"
    fi
fi

# 无沙箱启动参数
ARGS=(
    --no-sandbox
    --disable-web-security
    --disable-features=VizDisplayCompositor
    --disable-gpu-sandbox
    --disable-dev-shm-usage
    --disable-software-rasterizer
    --disable-background-timer-throttling
    --disable-backgrounding-occluded-windows
    --disable-renderer-backgrounding
    --disable-field-trial-config
    --disable-ipc-flooding-protection
    --allow-running-insecure-content
    --disable-site-isolation-trials
    --disable-hang-monitor
    --disable-domain-reliability
    --enable-logging=stderr
    --log-level=0
)

# 启动 VSCode
exec "$ELECTRON_BINARY" "${ARGS[@]}" "$@"
EOF

chmod +x "$VSCODE_ELECTRON.new"

# 3. 替换原始 Electron
echo "3. 替换原始 Electron..."
mv "$VSCODE_ELECTRON" "$VSCODE_ELECTRON.original"
mv "$VSCODE_ELECTRON.new" "$VSCODE_ELECTRON"

echo "✅ VSCode 应用程序包已修改"

# 4. 修改 Info.plist (可选)
echo "4. 修改 Info.plist..."
if [ -f "$VSCODE_INFO_PLIST" ]; then
    # 备份
    cp "$VSCODE_INFO_PLIST" "$VSCODE_INFO_PLIST.backup"
    
    # 添加环境变量到 Info.plist
    /usr/libexec/PlistBuddy -c "Add :LSEnvironment dict" "$VSCODE_INFO_PLIST" 2>/dev/null || true
    /usr/libexec/PlistBuddy -c "Add :LSEnvironment:ELECTRON_NO_SANDBOX string 1" "$VSCODE_INFO_PLIST" 2>/dev/null || true
    /usr/libexec/PlistBuddy -c "Add :LSEnvironment:CHROME_NO_SANDBOX string 1" "$VSCODE_INFO_PLIST" 2>/dev/null || true
    /usr/libexec/PlistBuddy -c "Add :LSEnvironment:DISABLE_SECCOMP_FILTER_SANDBOX string 1" "$VSCODE_INFO_PLIST" 2>/dev/null || true
    
    echo "✅ Info.plist 已修改"
fi

# 5. 清除应用程序缓存
echo "5. 清除应用程序缓存..."
rm -rf ~/Library/Caches/com.microsoft.VSCode
rm -rf ~/Library/Application\ Support/Code/CachedData
rm -rf ~/Library/Application\ Support/Code/logs

# 6. 重新签名应用程序 (如果需要)
echo "6. 重新签名应用程序..."
codesign --force --deep --sign - "$VSCODE_APP" 2>/dev/null || echo "签名失败，但应用程序仍可运行"

echo "=== 修改完成 ==="
echo "现在通过以下方式启动的 VSCode 都将无沙箱运行："
echo "- Dock 图标"
echo "- Launchpad"
echo "- Spotlight 搜索"
echo "- Finder 双击"
echo ""
echo "如果要恢复原始版本："
echo "mv '$VSCODE_ELECTRON.original' '$VSCODE_ELECTRON'"
echo "cp '$VSCODE_INFO_PLIST.backup' '$VSCODE_INFO_PLIST'"
