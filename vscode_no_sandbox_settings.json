{"window.titleBarStyle": "custom", "security.workspace.trust.enabled": false, "security.workspace.trust.banner": "never", "security.workspace.trust.startupPrompt": "never", "security.workspace.trust.emptyWindow": false, "extensions.autoCheckUpdates": false, "extensions.autoUpdate": false, "telemetry.telemetryLevel": "off", "update.mode": "none", "terminal.integrated.inheritEnv": false, "terminal.integrated.allowWorkspaceConfiguration": true, "terminal.integrated.env.osx": {"PATH": "/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin:/opt/homebrew/bin", "DISABLE_SANDBOX": "1", "NO_SANDBOX": "1"}, "terminal.integrated.profiles.osx": {"zsh (unrestricted)": {"path": "/bin/zsh", "args": ["-l", "-i"], "env": {"DISABLE_SANDBOX": "1", "NO_SANDBOX": "1"}}, "bash (unrestricted)": {"path": "/bin/bash", "args": ["-l", "-i"], "env": {"DISABLE_SANDBOX": "1", "NO_SANDBOX": "1"}}}, "terminal.integrated.defaultProfile.osx": "zsh (unrestricted)", "terminal.integrated.automationProfile.osx": {"path": "/bin/zsh", "args": ["-l", "-i"]}, "remote.SSH.useLocalServer": false, "remote.SSH.enableDynamicForwarding": true, "remote.SSH.serverPickPortsFromRange": {"22": "22"}}